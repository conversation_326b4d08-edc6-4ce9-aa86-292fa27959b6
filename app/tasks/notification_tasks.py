"""通知相关的Celery任务"""

from typing import Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.celery import app
from app.core.logging import logger
from app.db.session import SessionLocal
from app.services.websocket_manager import websocket_manager
from app.services.notification_service import NotificationService
from app.crud.notification import notification as crud_notification
from app.notifications.models import Notification, NotificationType, NotificationPriority
from app.notifications.schemas import NotificationResponse


@app.task(bind=True, name="app.tasks.notification_tasks.send_notification_websocket")
def send_notification_websocket(self, notification_id: int):
    """
    通过WebSocket发送通知
    
    Args:
        notification_id: 通知ID
    """
    try:
        import asyncio
        asyncio.run(_send_notification_websocket_async(notification_id))
        logger.info(f"WebSocket通知发送成功 - 通知ID: {notification_id}")
    except Exception as e:
        logger.error(f"WebSocket通知发送失败 - 通知ID: {notification_id}, 错误: {str(e)}")
        raise


async def _send_notification_websocket_async(notification_id: int):
    """异步发送WebSocket通知"""
    db = SessionLocal()
    try:
        # 获取通知详情
        stmt = select(Notification).where(Notification.id == notification_id)
        result = await db.execute(stmt)
        notification = result.scalar_one_or_none()
        
        if not notification:
            logger.warning(f"通知不存在 - ID: {notification_id}")
            return
        
        # 转换为响应模型
        notification_response = NotificationResponse.model_validate(notification)
        
        # 通过WebSocket发送通知
        await websocket_manager.send_notification(notification_response)
        
        # 发送未读数量更新
        unread_count = await crud_notification.get_unread_count(
            db=db, user_id=notification.user_id
        )
        await websocket_manager.send_unread_count_update(
            user_id=notification.user_id,
            unread_count=unread_count
        )
        
    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.update_unread_count")
def update_unread_count(self, user_id: int):
    """
    更新用户未读通知数量
    
    Args:
        user_id: 用户ID
    """
    try:
        import asyncio
        asyncio.run(_update_unread_count_async(user_id))
        logger.info(f"未读数量更新成功 - 用户ID: {user_id}")
    except Exception as e:
        logger.error(f"未读数量更新失败 - 用户ID: {user_id}, 错误: {str(e)}")
        raise


async def _update_unread_count_async(user_id: int):
    """异步更新未读数量"""
    db = SessionLocal()
    try:
        unread_count = await crud_notification.get_unread_count(
            db=db, user_id=user_id
        )
        
        # 通过WebSocket发送未读数量更新
        await websocket_manager.send_unread_count_update(
            user_id=user_id,
            unread_count=unread_count
        )
        
    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.create_content_notification")
def create_content_notification(self, content_type: str, content_id: int):
    """
    创建内容相关通知
    
    Args:
        content_type: 内容类型 (article, video)
        content_id: 内容ID
    """
    try:
        import asyncio
        asyncio.run(_create_content_notification_async(content_type, content_id))
        logger.info(f"内容通知创建成功 - 类型: {content_type}, ID: {content_id}")
    except Exception as e:
        logger.error(f"内容通知创建失败 - 类型: {content_type}, ID: {content_id}, 错误: {str(e)}")
        raise


async def _create_content_notification_async(content_type: str, content_id: int):
    """异步创建内容通知"""
    db = SessionLocal()
    notification_service = NotificationService()
    
    try:
        # 根据内容类型获取相关信息
        if content_type == "article":
            from app.crud.article import article as crud_article
            content = await crud_article.get(db, id=content_id)
            if content:
                # 通知关注该作者的用户
                await _notify_followers_about_new_content(
                    db, notification_service, content.author_id, 
                    "文章", content.title, content_id
                )
        elif content_type == "video":
            from app.crud.video import video as crud_video
            content = await crud_video.get(db, id=content_id)
            if content:
                # 通知关注该作者的用户
                await _notify_followers_about_new_content(
                    db, notification_service, content.author_id,
                    "视频", content.title, content_id
                )
                
    finally:
        await db.close()


async def _notify_followers_about_new_content(
    db: AsyncSession,
    notification_service: NotificationService,
    author_id: int,
    content_type: str,
    content_title: str,
    content_id: int
):
    """通知关注者有新内容"""
    from app.crud.user import user as crud_user
    
    # 获取作者信息
    author = await crud_user.get(db, id=author_id)
    if not author:
        return
    
    # 获取关注者列表
    followers = await crud_user.get_followers(db, user_id=author_id)
    
    # 为每个关注者创建通知
    for follower in followers:
        try:
            await notification_service.create_content_recommendation_notification(
                db=db,
                user_id=follower.id,
                content_title=content_title,
                content_type=content_type,
                content_id=content_id,
                action_url=f"/{content_type}s/{content_id}"
            )
        except Exception as e:
            logger.error(f"为用户 {follower.id} 创建内容通知失败: {str(e)}")


@app.task(bind=True, name="app.tasks.notification_tasks.cleanup_expired_notifications")
def cleanup_expired_notifications(self):
    """清理过期通知"""
    try:
        import asyncio
        count = asyncio.run(_cleanup_expired_notifications_async())
        logger.info(f"过期通知清理完成 - 清理数量: {count}")
        return count
    except Exception as e:
        logger.error(f"过期通知清理失败: {str(e)}")
        raise


async def _cleanup_expired_notifications_async() -> int:
    """异步清理过期通知"""
    db = SessionLocal()
    try:
        count = await crud_notification.cleanup_expired_notifications(db)
        return count
    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.send_system_broadcast")
def send_system_broadcast(self, title: str, message: str, data: Dict[str, Any] = None):
    """
    发送系统广播通知
    
    Args:
        title: 通知标题
        message: 通知内容
        data: 附加数据
    """
    try:
        import asyncio
        asyncio.run(_send_system_broadcast_async(title, message, data))
        logger.info(f"系统广播发送成功 - 标题: {title}")
    except Exception as e:
        logger.error(f"系统广播发送失败 - 标题: {title}, 错误: {str(e)}")
        raise


async def _send_system_broadcast_async(title: str, message: str, data: Dict[str, Any] = None):
    """异步发送系统广播"""
    await websocket_manager.broadcast_system_message(
        title=title,
        message=message,
        data=data
    )


# 定期任务：清理过期通知
@app.task(bind=True, name="app.tasks.notification_tasks.periodic_cleanup")
def periodic_cleanup_notifications(self):
    """定期清理过期通知（每天执行）"""
    try:
        count = cleanup_expired_notifications.delay()
        logger.info(f"定期清理任务已启动")
        return count
    except Exception as e:
        logger.error(f"定期清理任务启动失败: {str(e)}")
        raise
