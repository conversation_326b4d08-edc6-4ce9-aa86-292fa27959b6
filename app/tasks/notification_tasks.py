"""通知相关的Celery任务"""

from typing import Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.celery import app
from app.core.logging import logger
from app.crud.notification import notification as crud_notification
from app.db.session import SessionLocal
from app.notifications.models import Notification
from app.notifications.schemas import NotificationResponse
from app.services.notification_service import NotificationService
from app.services.websocket_manager import websocket_manager


@app.task(bind=True, name="app.tasks.notification_tasks.send_notification_websocket")
def send_notification_websocket(self, notification_id: int):
    """
    通过WebSocket发送通知

    Args:
        notification_id: 通知ID
    """
    try:
        import asyncio

        asyncio.run(_send_notification_websocket_async(notification_id))
        logger.info(f"WebSocket通知发送成功 - 通知ID: {notification_id}")
    except Exception as e:
        logger.error(f"WebSocket通知发送失败 - 通知ID: {notification_id}, 错误: {str(e)}")
        raise


async def _send_notification_websocket_async(notification_id: int):
    """异步发送WebSocket通知"""
    db = SessionLocal()
    try:
        # 获取通知详情
        stmt = select(Notification).where(Notification.id == notification_id)
        result = await db.execute(stmt)
        notification = result.scalar_one_or_none()

        if not notification:
            logger.warning(f"通知不存在 - ID: {notification_id}")
            return

        # 转换为响应模型
        notification_response = NotificationResponse.model_validate(notification)

        # 通过WebSocket发送通知
        await websocket_manager.send_notification(notification_response)

        # 发送未读数量更新
        unread_count = await crud_notification.get_unread_count(db=db, user_id=notification.user_id)
        await websocket_manager.send_unread_count_update(
            user_id=notification.user_id, unread_count=unread_count
        )

    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.update_unread_count")
def update_unread_count(self, user_id: int):
    """
    更新用户未读通知数量

    Args:
        user_id: 用户ID
    """
    try:
        import asyncio

        asyncio.run(_update_unread_count_async(user_id))
        logger.info(f"未读数量更新成功 - 用户ID: {user_id}")
    except Exception as e:
        logger.error(f"未读数量更新失败 - 用户ID: {user_id}, 错误: {str(e)}")
        raise


async def _update_unread_count_async(user_id: int):
    """异步更新未读数量"""
    db = SessionLocal()
    try:
        unread_count = await crud_notification.get_unread_count(db=db, user_id=user_id)

        # 通过WebSocket发送未读数量更新
        await websocket_manager.send_unread_count_update(user_id=user_id, unread_count=unread_count)

    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.create_content_notification")
def create_content_notification(self, content_type: str, content_id: int):
    """
    创建内容相关通知

    Args:
        content_type: 内容类型 (article, video)
        content_id: 内容ID
    """
    try:
        import asyncio

        asyncio.run(_create_content_notification_async(content_type, content_id))
        logger.info(f"内容通知创建成功 - 类型: {content_type}, ID: {content_id}")
    except Exception as e:
        logger.error(f"内容通知创建失败 - 类型: {content_type}, ID: {content_id}, 错误: {str(e)}")
        raise


async def _create_content_notification_async(content_type: str, content_id: int):
    """异步创建内容通知"""
    db = SessionLocal()
    notification_service = NotificationService()

    try:
        # 根据内容类型获取相关信息
        if content_type == "article":
            from app.crud.article import article as crud_article

            content = await crud_article.get(db, id=content_id)
            if content:
                # 通知关注该作者的用户
                await _notify_followers_about_new_content(
                    db, notification_service, content.author_id, "文章", content.title, content_id
                )
        elif content_type == "video":
            from app.crud.video import video as crud_video

            content = await crud_video.get(db, id=content_id)
            if content:
                # 通知关注该作者的用户
                await _notify_followers_about_new_content(
                    db, notification_service, content.author_id, "视频", content.title, content_id
                )

    finally:
        await db.close()


async def _notify_followers_about_new_content(
    db: AsyncSession,
    notification_service: NotificationService,
    author_id: int,
    content_type: str,
    content_title: str,
    content_id: int,
):
    """通知关注者有新内容"""
    from app.crud.user import user as crud_user

    # 获取作者信息
    author = await crud_user.get(db, id=author_id)
    if not author:
        return

    # 获取关注者列表
    followers = await crud_user.get_followers(db, user_id=author_id)

    # 为每个关注者创建通知
    for follower in followers:
        try:
            await notification_service.create_content_recommendation_notification(
                db=db,
                user_id=follower.id,
                content_title=content_title,
                content_type=content_type,
                content_id=content_id,
                action_url=f"/{content_type}s/{content_id}",
            )
        except Exception as e:
            logger.error(f"为用户 {follower.id} 创建内容通知失败: {str(e)}")


@app.task(bind=True, name="app.tasks.notification_tasks.cleanup_expired_notifications")
def cleanup_expired_notifications(self):
    """清理过期通知"""
    try:
        import asyncio

        count = asyncio.run(_cleanup_expired_notifications_async())
        logger.info(f"过期通知清理完成 - 清理数量: {count}")
        return count
    except Exception as e:
        logger.error(f"过期通知清理失败: {str(e)}")
        raise


async def _cleanup_expired_notifications_async() -> int:
    """异步清理过期通知"""
    db = SessionLocal()
    try:
        count = await crud_notification.cleanup_expired_notifications(db)
        return count
    finally:
        await db.close()


@app.task(bind=True, name="app.tasks.notification_tasks.send_system_broadcast")
def send_system_broadcast(self, title: str, message: str, data: dict[str, Any] = None):
    """
    发送系统广播通知

    Args:
        title: 通知标题
        message: 通知内容
        data: 附加数据
    """
    try:
        import asyncio

        asyncio.run(_send_system_broadcast_async(title, message, data))
        logger.info(f"系统广播发送成功 - 标题: {title}")
    except Exception as e:
        logger.error(f"系统广播发送失败 - 标题: {title}, 错误: {str(e)}")
        raise


async def _send_system_broadcast_async(title: str, message: str, data: dict[str, Any] = None):
    """异步发送系统广播"""
    await websocket_manager.broadcast_system_message(title=title, message=message, data=data)


# 定期任务：清理过期通知
@app.task(bind=True, name="app.tasks.notification_tasks.periodic_cleanup")
def periodic_cleanup_notifications(self):
    """定期清理过期通知（每天执行）"""
    try:
        count = cleanup_expired_notifications.delay()
        logger.info("定期清理任务已启动")
        return count
    except Exception as e:
        logger.error(f"定期清理任务启动失败: {str(e)}")
        raise


@app.task(bind=True, name="app.tasks.notification_tasks.broadcast_notification_task")
def broadcast_notification_task(self, task_data: dict[str, Any]):
    """
    广播通知任务（异步处理）

    Args:
        task_data: 包含通知信息的字典
    """
    try:
        import asyncio

        result = asyncio.run(_broadcast_notification_async(task_data))
        logger.info(f"广播通知任务完成 - 标题: {task_data.get('title')}, 创建数量: {result}")
        return result
    except Exception as e:
        logger.error(f"广播通知任务失败 - 标题: {task_data.get('title')}, 错误: {str(e)}")
        raise


async def _broadcast_notification_async(task_data: dict[str, Any]) -> int:
    """异步执行广播通知"""
    db = SessionLocal()
    notification_service = NotificationService()

    try:
        from sqlalchemy import select

        from app.models.user import User

        # 获取目标用户类型
        target_user_type = task_data.get("target_user_type", "all")

        # 构建用户查询
        if target_user_type == "all":
            stmt = select(User.id).where(User.is_active == True, User.is_deleted == False)
        elif target_user_type == "active":
            # 可以根据需要添加更多筛选条件
            stmt = select(User.id).where(User.is_active == True, User.is_deleted == False)
        else:
            stmt = select(User.id).where(User.is_active == True, User.is_deleted == False)

        # 分批处理用户
        BATCH_SIZE = 1000
        offset = 0
        total_created = 0

        while True:
            # 获取一批用户ID
            batch_stmt = stmt.limit(BATCH_SIZE).offset(offset)
            result = await db.execute(batch_stmt)
            user_ids = [row[0] for row in result.fetchall()]

            if not user_ids:
                break

            # 为这批用户创建通知
            batch_created = await _create_notifications_batch(db, user_ids, task_data)
            total_created += batch_created

            offset += BATCH_SIZE

            # 避免长时间占用数据库连接
            if offset % (BATCH_SIZE * 5) == 0:
                await db.commit()
                logger.info(f"已处理 {offset} 个用户，创建通知 {total_created} 条")

        await db.commit()
        logger.info(f"广播通知完成 - 总计创建 {total_created} 条通知")
        return total_created

    finally:
        await db.close()


async def _create_notifications_batch(
    db: AsyncSession, user_ids: list[int], task_data: dict[str, Any]
) -> int:
    """批量创建通知"""
    try:
        from datetime import datetime, timedelta

        from app.notifications.models import Notification, NotificationPriority, NotificationType

        # 解析任务数据
        notification_type = NotificationType(task_data["notification_type"])
        priority = NotificationPriority(task_data["priority"])

        # 计算过期时间
        expires_at = None
        if task_data.get("expires_in_hours"):
            expires_at = datetime.utcnow() + timedelta(hours=task_data["expires_in_hours"])

        # 准备批量插入数据
        notifications_data = []
        for user_id in user_ids:
            notification_data = {
                "user_id": user_id,
                "type": notification_type,
                "priority": priority,
                "title": task_data["title"],
                "message": task_data["message"],
                "data": task_data.get("data"),
                "action_url": task_data.get("action_url"),
                "expires_at": expires_at,
                "created_at": datetime.utcnow(),
            }
            notifications_data.append(notification_data)

        # 批量插入
        from sqlalchemy import insert

        stmt = insert(Notification).values(notifications_data)
        await db.execute(stmt)

        # 为每个用户创建WebSocket推送事件
        for user_id in user_ids:
            # 创建发件箱消息用于WebSocket推送
            from app.models.outbox import OutboxMessage

            outbox_msg = OutboxMessage(
                topic="notification.created",
                payload={
                    "user_id": user_id,
                    "type": notification_type.value,
                    "priority": priority.value,
                    "title": task_data["title"],
                    "message": task_data["message"],
                },
            )
            db.add(outbox_msg)

        return len(user_ids)

    except Exception as e:
        logger.error(f"批量创建通知失败: {str(e)}")
        raise
