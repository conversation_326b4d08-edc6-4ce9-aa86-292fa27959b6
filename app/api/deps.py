from typing import Annotated

from fastapi import Depends, HTTPException, Query, Request, status
from fastapi.security import OAuth2<PERSON><PERSON><PERSON>Bearer
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.config import get_settings
from app.db.session import get_db
from app.models.user import User, UserRole
from app.services.interfaces.token_service_interface import ITokenService
from app.services.service_factory import get_token_service

settings = get_settings()

# OAuth2密码流的令牌URL
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

# 可选的OAuth2密码流（用于支持游客访问）
oauth2_scheme_optional = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login", auto_error=False
)


class TokenData(BaseModel):
    username: str | None = None
    permissions: list[str] = []


async def get_token_from_header_or_cookie(
    request: Request, token_from_header: str | None = Depends(oauth2_scheme_optional)
) -> str | None:
    """
    从 Authorization 头或 Cookie 中获取 token
    优先级: Authorization Header > Cookie
    """
    if token_from_header:
        return token_from_header

    token_from_cookie = request.cookies.get("access_token")
    return token_from_cookie


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    token: str | None = Depends(get_token_from_header_or_cookie),
    token_service: ITokenService = Depends(get_token_service),
) -> User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )

    if not token:
        raise credentials_exception

    # 使用 TokenService 验证 token
    token_info = await token_service.verify_token(token)
    if not token_info:
        raise credentials_exception

    username = token_info.get("sub")
    if not username:
        raise credentials_exception

    # 查询用户（预加载 role 和 role.permissions 关系）
    from sqlalchemy.orm import selectinload

    result = await db.execute(
        select(User)
        .options(selectinload(User.role).selectinload(UserRole.permissions))
        .where(User.username == username)
    )
    user = result.scalar_one_or_none()
    if user is None:
        raise credentials_exception
    if not user.is_active:
        raise HTTPException(status_code=400, detail="用户未激活")

    return user


async def get_current_active_user(
    current_user: Annotated[User, Depends(get_current_user)],
) -> User:
    """获取当前激活用户"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户未激活")
    return current_user


async def get_current_active_superuser(
    current_user: Annotated[User, Depends(get_current_user)],
) -> User:
    """获取当前超级管理员用户"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="用户权限不足")
    return current_user


async def get_current_user_optional(
    db: AsyncSession = Depends(get_db),
    token: str | None = Depends(get_token_from_header_or_cookie),
    token_service: ITokenService = Depends(get_token_service),
) -> User | None:
    """
    获取当前用户（可选）
    用于支持游客访问的端点

    Args:
        db: 数据库会话
        token: 可选的访问令牌

    Returns:
        用户对象或None（游客）
    """
    if not token:
        return None

    # 验证 token，但失败时返回 None 而不是抛出异常
    token_info = await token_service.verify_token(token)
    if not token_info:
        return None

    username = token_info.get("sub")
    if not username:
        return None

    # 查询用户
    from sqlalchemy.orm import selectinload

    result = await db.execute(
        select(User)
        .options(selectinload(User.role).selectinload(UserRole.permissions))
        .where(User.username == username)
    )
    user = result.scalar_one_or_none()

    # 如果用户不存在或未激活，也视为游客
    if user is None or not user.is_active:
        return None

    return user


async def get_current_user_websocket(
    websocket,
    token: str = Query(..., description="访问令牌"),
    db: AsyncSession = Depends(get_db),
    token_service: ITokenService = Depends(get_token_service),
) -> User:
    """WebSocket连接的用户认证"""
    try:
        # 验证token
        payload = await token_service.verify_token(token)
        user_id = payload.get("sub")

        if user_id is None:
            await websocket.close(code=1008, reason="Invalid token")
            raise HTTPException(status_code=401, detail="无效的token")

        # 获取用户信息
        result = await db.execute(select(User).where(User.id == int(user_id)))
        user = result.scalar_one_or_none()

        if user is None:
            await websocket.close(code=1008, reason="User not found")
            raise HTTPException(status_code=404, detail="用户不存在")

        if not user.is_active:
            await websocket.close(code=1008, reason="User inactive")
            raise HTTPException(status_code=400, detail="用户未激活")

        return user

    except Exception as e:
        await websocket.close(code=1008, reason=str(e))
        raise
