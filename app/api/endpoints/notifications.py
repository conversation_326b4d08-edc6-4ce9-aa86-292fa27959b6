"""通知相关API端点"""

import uuid
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.models.user import User
from app.services.notification_service import NotificationService
from app.services.websocket_manager import websocket_handler, websocket_manager
from app.notifications.schemas import (
    NotificationCreate,
    NotificationResponse,
    NotificationListResponse,
    UnreadCountResponse
)
from app.notifications.models import NotificationStatus
from app.core.logging import logger

router = APIRouter()


def get_notification_service() -> NotificationService:
    """获取通知服务实例"""
    return NotificationService()


@router.get("/", response_model=NotificationListResponse, summary="获取通知列表")
async def get_notifications(
    status: Optional[NotificationStatus] = Query(None, description="通知状态筛选"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service)
) -> NotificationListResponse:
    """
    获取当前用户的通知列表
    
    - **status**: 可选的状态筛选 (unread, read, deleted)
    - **limit**: 每页返回的通知数量 (1-100)
    - **offset**: 分页偏移量
    """
    try:
        return await notification_service.get_user_notifications(
            db=db,
            user_id=current_user.id,
            status=status,
            limit=limit,
            offset=offset
        )
    except Exception as e:
        logger.error(f"获取通知列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取通知列表失败")


@router.get("/unread-count", response_model=UnreadCountResponse, summary="获取未读通知数量")
async def get_unread_count(
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service)
) -> UnreadCountResponse:
    """获取当前用户的未读通知数量"""
    try:
        return await notification_service.get_unread_count(
            db=db, user_id=current_user.id
        )
    except Exception as e:
        logger.error(f"获取未读通知数量失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取未读通知数量失败")


@router.post("/", response_model=NotificationResponse, summary="创建通知")
async def create_notification(
    notification_in: NotificationCreate,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service)
) -> NotificationResponse:
    """
    创建新通知（管理员功能）
    
    注意：此接口通常用于系统内部或管理员创建通知
    """
    try:
        return await notification_service.create_notification(
            db=db,
            user_id=notification_in.user_id,
            notification_type=notification_in.type,
            title=notification_in.title,
            message=notification_in.message,
            priority=notification_in.priority,
            data=notification_in.data,
            action_url=notification_in.action_url,
            expires_in_hours=notification_in.expires_in_hours
        )
    except Exception as e:
        logger.error(f"创建通知失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建通知失败")


@router.patch("/{notification_id}/read", response_model=NotificationResponse, summary="标记通知为已读")
async def mark_notification_as_read(
    notification_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service)
) -> NotificationResponse:
    """标记指定通知为已读"""
    try:
        notification = await notification_service.mark_as_read(
            db=db,
            notification_id=notification_id,
            user_id=current_user.id
        )
        
        if not notification:
            raise HTTPException(status_code=404, detail="通知不存在或无权限访问")
        
        return notification
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"标记通知为已读失败: {str(e)}")
        raise HTTPException(status_code=500, detail="标记通知为已读失败")


@router.patch("/read-all", summary="标记所有通知为已读")
async def mark_all_notifications_as_read(
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service)
) -> dict:
    """标记当前用户的所有未读通知为已读"""
    try:
        count = await notification_service.mark_all_as_read(
            db=db, user_id=current_user.id
        )
        
        return {
            "message": f"成功标记 {count} 条通知为已读",
            "count": count
        }
    except Exception as e:
        logger.error(f"批量标记通知为已读失败: {str(e)}")
        raise HTTPException(status_code=500, detail="批量标记通知为已读失败")


@router.delete("/{notification_id}", summary="删除通知")
async def delete_notification(
    notification_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    notification_service: NotificationService = Depends(get_notification_service)
) -> dict:
    """删除指定通知"""
    try:
        success = await notification_service.delete_notification(
            db=db,
            notification_id=notification_id,
            user_id=current_user.id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="通知不存在或无权限访问")
        
        return {"message": "通知删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除通知失败: {str(e)}")
        raise HTTPException(status_code=500, detail="删除通知失败")


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    db: AsyncSession = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user_websocket)
):
    """
    WebSocket端点用于实时通知推送
    
    连接后会自动接收：
    - 新通知消息
    - 未读数量更新
    - 系统通知
    """
    connection_id = str(uuid.uuid4())
    
    try:
        await websocket_handler.handle_connection(
            websocket=websocket,
            user_id=current_user.id,
            connection_id=connection_id,
            db=db
        )
    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开 - 用户ID: {current_user.id}")
    except Exception as e:
        logger.error(f"WebSocket连接异常: {str(e)}")


@router.get("/stats", summary="获取通知统计信息")
async def get_notification_stats(
    current_user: User = Depends(deps.get_current_user)
) -> dict:
    """
    获取通知相关统计信息（管理员功能）
    
    包括：
    - WebSocket连接统计
    - 在线用户数量
    """
    try:
        connection_stats = websocket_manager.get_connection_stats()
        online_users = websocket_manager.get_online_users()
        
        return {
            "websocket_stats": connection_stats,
            "online_user_count": len(online_users),
            "current_user_connections": websocket_manager.get_user_connection_count(current_user.id)
        }
    except Exception as e:
        logger.error(f"获取通知统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")
